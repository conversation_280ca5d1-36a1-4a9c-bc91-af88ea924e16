import { LitElement, css, html } from 'lit';
import { customElement, query } from 'lit/decorators.js';
import { SignalWatcher } from '@lit-labs/signals';
import { sanitizeRichText } from '@utils/sanitize';

/**
 * Helper function to replace a node with sanitized HTML content
 * Uses a temporary div to convert HTML string to DOM nodes
 * @param node - The node to replace
 * @param sanitizedHTML - The sanitized HTML string
 */
function replaceNodeWithSanitizedHTML(node: Node, sanitizedHTML: string): void {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = sanitizedHTML;

  if (node.parentNode) {
    if (node.nodeType === Node.ELEMENT_NODE) {
      // For element nodes, replace with the first child of the temp div
      const sanitizedElement = tempDiv.firstChild;
      if (sanitizedElement) {
        node.parentNode.replaceChild(sanitizedElement, node);
      }
    } else {
      // For text nodes, replace with all children of the temp div
      while (tempDiv.firstChild) {
        node.parentNode.insertBefore(tempDiv.firstChild, node);
      }
      node.parentNode.removeChild(node);
    }
  }
}

/**
 * Component for safely displaying formatted text content using slots
 * Sanitizes HTML content to prevent XSS while preserving formatting
 */
@customElement('formatted-text')
export class FormattedText extends SignalWatcher(LitElement) {
  static styles = css`
    :host {
      display: block;
      position: relative; /* Needed for absolute positioning of the overlay */
      overflow: hidden;
      padding-bottom: var(--fade-height, 0rem);
    }

    .overlay {
      position: absolute; /* Position over the slot */
      bottom: 0;
      left: 0;
      width: 100%;
      height: var(--fade-height, 0rem);
      background: linear-gradient(transparent, var(--fade-color, transparent));
    }
  `;

  @query('slot', true) slotContent!: HTMLSlotElement;

  private lastProcessedContent = '';
  private sanitizeTimeout: number | null = null;

  private sanitize() {
    if (this.slotContent) {
      const assignedNodes = this.slotContent.assignedNodes();

      // Get current content to check if it has changed
      const currentContent = assignedNodes
        .map(node => node.textContent || '')
        .join('');

      // Only process if content has actually changed
      if (currentContent === this.lastProcessedContent) {
        return;
      }

      this.lastProcessedContent = currentContent;

      assignedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // For element nodes, sanitize the HTML content
          const element = node as Element;
          const sanitizedHTML = sanitizeRichText(element.outerHTML);
          replaceNodeWithSanitizedHTML(node, sanitizedHTML);
        } else if (node.nodeType === Node.TEXT_NODE) {
          // For text nodes, check if they contain HTML and process accordingly
          const textContent = node.textContent || '';

          // If text contains HTML tags, sanitize it
          if (/<[^>]+>/.test(textContent)) {
            const sanitizedHTML = sanitizeRichText(textContent);
            replaceNodeWithSanitizedHTML(node, sanitizedHTML);
          }
          // Plain text nodes are left as-is (they're safe)
        }
      });
    }
  }

  private debouncedSanitize() {
    // Clear any existing timeout
    if (this.sanitizeTimeout !== null) {
      clearTimeout(this.sanitizeTimeout);
    }

    // Schedule sanitization for next microtask to ensure DOM is updated
    this.sanitizeTimeout = window.setTimeout(() => {
      this.sanitize();
      this.sanitizeTimeout = null;
    }, 0);
  }

  private handleSlotChange = () => {
    // Use debounced sanitization when slot content changes
    this.debouncedSanitize();
  };

  firstUpdated() {
    // Set up slot change listener
    if (this.slotContent) {
      this.slotContent.addEventListener('slotchange', this.handleSlotChange);
    }
    // Sanitize content after the component is first rendered
    this.sanitize();
  }

  updated() {
    // Use debounced sanitization when the component updates
    this.debouncedSanitize();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    // Clean up timeout and event listener
    if (this.sanitizeTimeout !== null) {
      clearTimeout(this.sanitizeTimeout);
      this.sanitizeTimeout = null;
    }
    if (this.slotContent) {
      this.slotContent.removeEventListener('slotchange', this.handleSlotChange);
    }
  }

  render() {
    return html`
      <slot></slot>
      <div class="overlay"></div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'formatted-text': FormattedText;
  }
}
